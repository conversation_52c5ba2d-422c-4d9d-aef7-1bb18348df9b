#!/bin/bash

# Test script for MCP Server
echo "Testing MCP Server..."

# Start the MCP server in the background
java -jar target/remodern-java-1.0.0-SNAPSHOT-mcp-server.jar &
SERVER_PID=$!

# Give the server time to start
sleep 2

# Test initialize
echo '{"id":1,"method":"initialize","params":{"protocolVersion":"2024-11-05","capabilities":{"experimental":{},"sampling":{}},"clientInfo":{"name":"test-client","version":"1.0.0"}},"jsonrpc":"2.0"}' | java -jar target/remodern-java-1.0.0-SNAPSHOT-mcp-server.jar &
INIT_PID=$!

sleep 1

# Test tools/list
echo '{"id":2,"method":"tools/list","params":{},"jsonrpc":"2.0"}' | java -jar target/remodern-java-1.0.0-SNAPSHOT-mcp-server.jar &
LIST_PID=$!

sleep 1

# Test tool call
echo '{"id":3,"method":"tools/call","params":{"name":"test","arguments":{"message":"Hello MCP!"}},"jsonrpc":"2.0"}' | java -jar target/remodern-java-1.0.0-SNAPSHOT-mcp-server.jar &
CALL_PID=$!

sleep 2

# Clean up
kill $SERVER_PID $INIT_PID $LIST_PID $CALL_PID 2>/dev/null

echo "MCP Server test completed."
