package com.phodal.remodern.mcp;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.phodal.remodern.core.McpTool;
import com.phodal.remodern.core.McpToolRegistry;
import com.phodal.remodern.core.McpToolResult;
import com.phodal.remodern.tools.bytecode.ByteCodeTool;
import com.phodal.remodern.tools.codegen.AstCodeGenTool;
import com.phodal.remodern.tools.codegen.TemplateCodeGenTool;
import com.phodal.remodern.tools.migration.OpenRewriteTool;
import com.phodal.remodern.tools.parsing.JSPParseTool;
import com.phodal.remodern.tools.parsing.JavaParseTool;
import io.modelcontextprotocol.sdk.server.McpServer;
import io.modelcontextprotocol.sdk.server.McpServerFeatures;
import io.modelcontextprotocol.sdk.server.transport.StdioServerTransportProvider;
import io.modelcontextprotocol.sdk.shared.CallToolResult;
import io.modelcontextprotocol.sdk.shared.McpSchema;
import io.modelcontextprotocol.sdk.shared.Tool;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * Official MCP Server implementation using the MCP Java SDK.
 * Provides Java development tools through the Model Context Protocol.
 */
public class OfficialMcpServer {
    
    private static final Logger logger = LoggerFactory.getLogger(OfficialMcpServer.class);
    
    private final McpToolRegistry toolRegistry;
    
    public OfficialMcpServer() {
        this.toolRegistry = McpToolRegistry.getInstance();
        registerTools();
    }
    
    public static void main(String[] args) {
        try {
            OfficialMcpServer server = new OfficialMcpServer();
            server.start();
        } catch (Exception e) {
            logger.error("Failed to start MCP server", e);
            System.exit(1);
        }
    }
    
    public void start() throws Exception {
        logger.info("Starting ReModern Java MCP Server...");
        
        // Create STDIO transport provider
        StdioServerTransportProvider transportProvider = new StdioServerTransportProvider();
        
        // Configure server capabilities
        McpSchema.ServerCapabilities capabilities = McpSchema.ServerCapabilities.builder()
            .tools(true) // Enable tools with list changes notifications
            .logging() // Enable logging support
            .build();
        
        // Create tool specifications
        List<McpServerFeatures.SyncToolSpecification> toolSpecs = createToolSpecifications();
        
        // Create and start the server
        var mcpServer = McpServer.sync(transportProvider)
            .serverInfo("ReModern Java MCP Server", "1.0.0")
            .capabilities(capabilities)
            .tools(toolSpecs)
            .build();
        
        logger.info("MCP Server started successfully with {} tools", toolRegistry.size());
        logger.info("Registered tools: {}", toolRegistry.getToolNames());
        
        // Keep the server running
        mcpServer.start();
    }
    
    private void registerTools() {
        try {
            // Register all available tools
            toolRegistry.registerTool(new AstCodeGenTool());
            toolRegistry.registerTool(new TemplateCodeGenTool());
            toolRegistry.registerTool(new JavaParseTool());
            toolRegistry.registerTool(new JSPParseTool());
            toolRegistry.registerTool(new ByteCodeTool());
            toolRegistry.registerTool(new OpenRewriteTool());
            
            logger.info("Registered {} tools", toolRegistry.size());
        } catch (Exception e) {
            logger.error("Failed to register tools", e);
            throw new RuntimeException("Tool registration failed", e);
        }
    }
    
    private List<McpServerFeatures.SyncToolSpecification> createToolSpecifications() {
        List<McpServerFeatures.SyncToolSpecification> toolSpecs = new ArrayList<>();
        
        for (McpTool tool : toolRegistry.getAllTools()) {
            try {
                // Create MCP Tool from our tool
                Tool mcpTool = Tool.builder()
                    .name(tool.getName())
                    .description(tool.getDescription())
                    .inputSchema(tool.getInputSchema())
                    .build();
                
                // Create tool specification with handler
                McpServerFeatures.SyncToolSpecification toolSpec = 
                    new McpServerFeatures.SyncToolSpecification(
                        mcpTool,
                        (exchange, args) -> {
                            try {
                                logger.debug("Executing tool: {} with args: {}", tool.getName(), args);
                                
                                // Execute the tool
                                McpToolResult result = toolRegistry.executeTool(tool.getName(), args);
                                
                                if (result.isSuccess()) {
                                    logger.debug("Tool {} executed successfully", tool.getName());
                                    
                                    // Create successful result
                                    CallToolResult.Builder resultBuilder = CallToolResult.builder()
                                        .content(List.of(
                                            McpSchema.TextContent.builder()
                                                .type("text")
                                                .text(result.getContent())
                                                .build()
                                        ));
                                    
                                    // Add metadata if available
                                    if (result.getMetadata() != null && !result.getMetadata().isEmpty()) {
                                        resultBuilder.meta(result.getMetadata());
                                    }
                                    
                                    return resultBuilder.build();
                                } else {
                                    logger.error("Tool {} execution failed: {}", tool.getName(), result.getErrorMessage());
                                    
                                    return CallToolResult.builder()
                                        .content(List.of(
                                            McpSchema.TextContent.builder()
                                                .type("text")
                                                .text("Error: " + result.getErrorMessage())
                                                .build()
                                        ))
                                        .isError(true)
                                        .build();
                                }
                                
                            } catch (Exception e) {
                                logger.error("Tool execution failed for {}", tool.getName(), e);
                                
                                return CallToolResult.builder()
                                    .content(List.of(
                                        McpSchema.TextContent.builder()
                                            .type("text")
                                            .text("Tool execution failed: " + e.getMessage())
                                            .build()
                                    ))
                                    .isError(true)
                                    .build();
                            }
                        }
                    );
                
                toolSpecs.add(toolSpec);
                logger.debug("Created tool specification for: {}", tool.getName());
                
            } catch (Exception e) {
                logger.error("Failed to create tool specification for: {}", tool.getName(), e);
            }
        }
        
        return toolSpecs;
    }
    
    /**
     * Graceful shutdown hook.
     */
    public void shutdown() {
        logger.info("Shutting down MCP server...");
        toolRegistry.clear();
        logger.info("MCP server shutdown complete");
    }
}
