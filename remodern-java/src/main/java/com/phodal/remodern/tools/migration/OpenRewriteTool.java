package com.phodal.remodern.tools.migration;

import com.phodal.remodern.core.McpTool;
import com.phodal.remodern.core.McpToolException;
import com.phodal.remodern.core.McpToolResult;
import org.openrewrite.*;
import org.openrewrite.config.Environment;
import org.openrewrite.java.JavaParser;
import org.openrewrite.java.JavaVisitor;
import org.openrewrite.java.tree.J;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.*;
import java.util.stream.Collectors;

/**
 * AST migration and refactoring tool using OpenRewrite.
 */
public class OpenRewriteTool implements McpTool {
    
    private static final String TOOL_NAME = "openrewrite";
    private static final String TOOL_DESCRIPTION = "Perform AST migration and refactoring using OpenRewrite";
    
    @Override
    public String getName() {
        return TOOL_NAME;
    }
    
    @Override
    public String getDescription() {
        return TOOL_DESCRIPTION;
    }
    
    @Override
    public Map<String, Object> getInputSchema() {
        Map<String, Object> schema = new HashMap<>();
        schema.put("type", "object");
        
        Map<String, Object> properties = new HashMap<>();
        
        // Required properties
        properties.put("operation", Map.of(
            "type", "string",
            "enum", Arrays.asList("recipe", "visitor", "refactor", "migrate"),
            "description", "Type of operation to perform"
        ));
        
        properties.put("source", Map.of(
            "type", "string",
            "description", "Source file or directory path"
        ));
        
        // Optional properties
        properties.put("recipe", Map.of(
            "type", "string",
            "description", "Recipe name (for recipe operation)"
        ));
        
        properties.put("visitor", Map.of(
            "type", "string",
            "description", "Custom visitor class (for visitor operation)"
        ));
        
        properties.put("rules", Map.of(
            "type", "array",
            "items", Map.of("type", "string"),
            "description", "List of refactoring rules (for refactor operation)"
        ));
        
        properties.put("migrationTarget", Map.of(
            "type", "string",
            "description", "Target framework/version (for migrate operation)"
        ));
        
        properties.put("dryRun", Map.of(
            "type", "boolean",
            "description", "Perform dry run without changes (default: false)"
        ));
        
        schema.put("properties", properties);
        schema.put("required", Arrays.asList("operation", "source"));
        
        return schema;
    }
    
    @Override
    public McpToolResult execute(Map<String, Object> args) throws McpToolException {
        try {
            String operation = (String) args.get("operation");
            String source = (String) args.get("source");
            boolean dryRun = (Boolean) args.getOrDefault("dryRun", false);
            
            Path sourcePath = Paths.get(source);
            
            if (!Files.exists(sourcePath)) {
                throw new McpToolException(TOOL_NAME, "Source path does not exist: " + source);
            }
            
            List<Path> javaFiles = collectJavaFiles(sourcePath);
            if (javaFiles.isEmpty()) {
                throw new McpToolException(TOOL_NAME, "No Java files found in: " + source);
            }
            
            Map<String, Object> result = switch (operation.toLowerCase()) {
                case "recipe" -> executeRecipe(args, javaFiles, dryRun);
                case "visitor" -> executeVisitor(args, javaFiles, dryRun);
                case "refactor" -> executeRefactor(args, javaFiles, dryRun);
                case "migrate" -> executeMigrate(args, javaFiles, dryRun);
                default -> throw new McpToolException(TOOL_NAME, "Unsupported operation: " + operation);
            };
            
            String resultContent = formatResult(result);
            
            Map<String, Object> metadata = new HashMap<>();
            metadata.put("operation", operation);
            metadata.put("processedFiles", javaFiles.stream().map(Path::toString).collect(Collectors.toList()));
            metadata.put("dryRun", dryRun);
            
            return McpToolResult.success(resultContent, metadata);
            
        } catch (Exception e) {
            throw new McpToolException(TOOL_NAME, "OpenRewrite operation failed", e);
        }
    }
    
    private List<Path> collectJavaFiles(Path sourcePath) throws IOException {
        if (Files.isDirectory(sourcePath)) {
            return Files.walk(sourcePath)
                .filter(path -> path.toString().endsWith(".java"))
                .collect(Collectors.toList());
        } else if (sourcePath.toString().endsWith(".java")) {
            return Arrays.asList(sourcePath);
        } else {
            return Collections.emptyList();
        }
    }
    
    @SuppressWarnings("unchecked")
    private Map<String, Object> executeRecipe(Map<String, Object> args, List<Path> javaFiles, boolean dryRun) 
            throws Exception {
        
        String recipeName = (String) args.get("recipe");
        if (recipeName == null || recipeName.isEmpty()) {
            throw new McpToolException(TOOL_NAME, "Recipe name is required for recipe operation");
        }
        
        Map<String, Object> result = new HashMap<>();
        
        // Create environment and load recipes
        Environment environment = Environment.builder()
            .scanRuntimeClasspath()
            .build();
        
        Recipe recipe = environment.activateRecipes(recipeName);
        
        // Parse Java files
        JavaParser javaParser = JavaParser.fromJavaVersion().build();
        List<SourceFile> sourceFiles = javaParser.parse(javaFiles, null, new InMemoryExecutionContext());
        
        // Execute recipe
        List<Result> results = recipe.run(sourceFiles);
        
        List<Map<String, Object>> changes = new ArrayList<>();
        for (Result recipeResult : results) {
            if (recipeResult.getBefore() != null && recipeResult.getAfter() != null) {
                Map<String, Object> change = new HashMap<>();
                change.put("file", recipeResult.getBefore().getSourcePath().toString());
                change.put("recipeName", recipeName);
                
                if (!dryRun) {
                    // Write changes to file
                    Files.writeString(recipeResult.getBefore().getSourcePath(), recipeResult.getAfter().printAll());
                    change.put("applied", true);
                } else {
                    change.put("applied", false);
                    change.put("preview", recipeResult.getAfter().printAll());
                }
                
                changes.add(change);
            }
        }
        
        result.put("recipe", recipeName);
        result.put("changes", changes);
        result.put("changedFiles", changes.size());
        
        return result;
    }
    
    private Map<String, Object> executeVisitor(Map<String, Object> args, List<Path> javaFiles, boolean dryRun) 
            throws Exception {
        
        String visitorClass = (String) args.get("visitor");
        if (visitorClass == null || visitorClass.isEmpty()) {
            throw new McpToolException(TOOL_NAME, "Visitor class is required for visitor operation");
        }
        
        Map<String, Object> result = new HashMap<>();
        
        // For demonstration, we'll create a simple visitor that counts method declarations
        JavaParser javaParser = JavaParser.fromJavaVersion().build();
        List<SourceFile> sourceFiles = javaParser.parse(javaFiles, null, new InMemoryExecutionContext());
        
        List<Map<String, Object>> analysis = new ArrayList<>();
        
        for (SourceFile sourceFile : sourceFiles) {
            if (sourceFile instanceof J.CompilationUnit) {
                J.CompilationUnit cu = (J.CompilationUnit) sourceFile;
                
                // Simple visitor to count methods
                MethodCountVisitor visitor = new MethodCountVisitor();
                visitor.visit(cu, 0);
                
                Map<String, Object> fileAnalysis = new HashMap<>();
                fileAnalysis.put("file", cu.getSourcePath().toString());
                fileAnalysis.put("methodCount", visitor.getMethodCount());
                fileAnalysis.put("classCount", visitor.getClassCount());
                
                analysis.add(fileAnalysis);
            }
        }
        
        result.put("visitor", visitorClass);
        result.put("analysis", analysis);
        result.put("processedFiles", analysis.size());
        
        return result;
    }
    
    @SuppressWarnings("unchecked")
    private Map<String, Object> executeRefactor(Map<String, Object> args, List<Path> javaFiles, boolean dryRun) 
            throws Exception {
        
        List<String> rules = (List<String>) args.get("rules");
        if (rules == null || rules.isEmpty()) {
            throw new McpToolException(TOOL_NAME, "Refactoring rules are required for refactor operation");
        }
        
        Map<String, Object> result = new HashMap<>();
        
        // Create a composite recipe from the rules
        Environment environment = Environment.builder()
            .scanRuntimeClasspath()
            .build();
        
        Recipe compositeRecipe = Recipe.noop();
        for (String rule : rules) {
            Recipe ruleRecipe = environment.activateRecipes(rule);
            compositeRecipe = compositeRecipe.doNext(ruleRecipe);
        }
        
        // Parse and execute
        JavaParser javaParser = JavaParser.fromJavaVersion().build();
        List<SourceFile> sourceFiles = javaParser.parse(javaFiles, null, new InMemoryExecutionContext());
        
        List<Result> results = compositeRecipe.run(sourceFiles);
        
        List<Map<String, Object>> changes = new ArrayList<>();
        for (Result refactorResult : results) {
            if (refactorResult.getBefore() != null && refactorResult.getAfter() != null) {
                Map<String, Object> change = new HashMap<>();
                change.put("file", refactorResult.getBefore().getSourcePath().toString());
                change.put("rules", rules);
                
                if (!dryRun) {
                    Files.writeString(refactorResult.getBefore().getSourcePath(), refactorResult.getAfter().printAll());
                    change.put("applied", true);
                } else {
                    change.put("applied", false);
                    change.put("preview", refactorResult.getAfter().printAll());
                }
                
                changes.add(change);
            }
        }
        
        result.put("rules", rules);
        result.put("changes", changes);
        result.put("changedFiles", changes.size());
        
        return result;
    }
    
    private Map<String, Object> executeMigrate(Map<String, Object> args, List<Path> javaFiles, boolean dryRun) 
            throws Exception {
        
        String migrationTarget = (String) args.get("migrationTarget");
        if (migrationTarget == null || migrationTarget.isEmpty()) {
            throw new McpToolException(TOOL_NAME, "Migration target is required for migrate operation");
        }
        
        Map<String, Object> result = new HashMap<>();
        
        // Map migration targets to recipes
        String recipeName = switch (migrationTarget.toLowerCase()) {
            case "junit5" -> "org.openrewrite.java.testing.junit5.JUnit4to5Migration";
            case "spring-boot-2.7" -> "org.openrewrite.java.spring.boot2.SpringBoot2_7";
            case "java11" -> "org.openrewrite.java.migrate.Java8toJava11";
            case "java17" -> "org.openrewrite.java.migrate.JavaVersion17";
            default -> throw new McpToolException(TOOL_NAME, "Unsupported migration target: " + migrationTarget);
        };
        
        // Execute the migration recipe
        Map<String, Object> recipeArgs = new HashMap<>(args);
        recipeArgs.put("recipe", recipeName);
        
        Map<String, Object> migrationResult = executeRecipe(recipeArgs, javaFiles, dryRun);
        
        result.put("migrationTarget", migrationTarget);
        result.put("recipe", recipeName);
        result.putAll(migrationResult);
        
        return result;
    }
    
    private String formatResult(Map<String, Object> result) {
        StringBuilder sb = new StringBuilder();
        sb.append("{\n");
        
        result.forEach((key, value) -> {
            sb.append("  \"").append(key).append("\": ");
            if (value instanceof String) {
                sb.append("\"").append(value).append("\"");
            } else {
                sb.append(value.toString());
            }
            sb.append(",\n");
        });
        
        if (sb.length() > 2) {
            sb.setLength(sb.length() - 2);
        }
        
        sb.append("\n}");
        return sb.toString();
    }
    
    /**
     * Simple visitor to count methods and classes.
     */
    private static class MethodCountVisitor extends JavaVisitor<Integer> {
        private int methodCount = 0;
        private int classCount = 0;
        
        @Override
        public J visitMethodDeclaration(J.MethodDeclaration method, Integer p) {
            methodCount++;
            return super.visitMethodDeclaration(method, p);
        }
        
        @Override
        public J visitClassDeclaration(J.ClassDeclaration classDecl, Integer p) {
            classCount++;
            return super.visitClassDeclaration(classDecl, p);
        }
        
        public int getMethodCount() { return methodCount; }
        public int getClassCount() { return classCount; }
    }
}
